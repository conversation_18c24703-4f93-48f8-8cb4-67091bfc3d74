# Augment Guidelines for solid-camera

This file provides comprehensive guidance for working with the solid-camera codebase, a React/TypeScript agricultural business administration application built with Supabase.

## Project Overview

**Repository:** solid-camera
**Application Brand:** NewTerra
**Business Domain:** Agricultural business administration and compliance services for Australian farms
**Architecture:** React 18 + TypeScript + Vite + Supabase + shadcn/ui

### What is NewTerra?

NewTerra is an agricultural business administration service that provides professional bookkeeping, compliance, and administrative services designed specifically for Australian agriculture. The application helps farmers:

- **Outsource Farm Business Administration** - Professional bookkeeping and compliance management
- **Save Money** - Reduce administrative costs by $2,000-$4,000 annually
- **Focus on Farming** - Eliminate administrative headaches and paperwork
- **Ensure Compliance** - Handle ATO deadlines, biosecurity requirements, WorkCover obligations

### Core Application Features

The primary application is a **multi-step onboarding wizard** that collects comprehensive business, operational, and financial data from new farming clients to enable outsourced farm business administration services.

**4-Step Onboarding Process:**
1. **Business Profile** - Registration details, contacts, addresses
2. **Farm Operations** - Activities, licenses, suppliers, contracts
3. **Financial Systems** - Bookkeeping, payroll, assets
4. **Agreements & Review** - Data migration, permissions, digital signatures

## Table of Contents

1. [Development Commands](#development-commands)
2. [Quick Start](#quick-start)
3. [ESLint & TypeScript Best Practices](#eslint--typescript-best-practices)
4. [Technology Stack](#technology-stack)
5. [Core Business Domain](#core-business-domain)
6. [Database Architecture](#database-architecture)
7. [Hook System (Consolidated)](#hook-system-consolidated)
8. [Edge Functions (Supabase)](#edge-functions-supabase)
9. [Component Architecture](#component-architecture)
10. [Form Components](#form-components)
11. [Utility Functions](#utility-functions)
12. [Validation & Formatting](#validation--formatting)
13. [Error Handling & Logging](#error-handling--logging)
14. [Performance Optimization](#performance-optimization)
15. [Testing Strategy](#testing-strategy)
16. [Security Best Practices](#security-best-practices)
17. [Deployment & Environment Configuration](#deployment--environment-configuration)
18. [Current Development Status & Roadmap](#current-development-status--roadmap)

## Development Commands

```bash
# Development server
npm run dev

# Build for production
npm run build

# Build for development
npm run build:dev

# Lint code
npm run lint

# Preview production build
npm run preview

# Run tests
npm run test
```

## Quick Start

1. **Clone and Install**:
   ```bash
   git clone https://github.com/tarek-fahmi/solid-camera.git
   cd solid-camera
   npm install
   ```

2. **Environment Setup**:
   ```bash
   # Copy environment template and configure
   cp .env.example .env  # (if available)
   # Add your Supabase credentials:
   # VITE_SUPABASE_URL=your_supabase_url
   # VITE_SUPABASE_ANON_KEY=your_anon_key
   ```

3. **Start Development**:
   ```bash
   npm run dev
   ```

## ESLint & TypeScript Best Practices

### Code Standards
- **Always use explicit type annotations** from `@/types/database.types.ts`
- **Use generic CRUD functions** from OnboardingContext instead of direct Supabase calls
- **Implement proper error handling** with try-catch blocks and user feedback
- **Follow React Hook patterns** - use consolidated hooks from `@/hooks/`
- **Validate Australian business formats** - ABN (`XX XXX XXX XXX`), ACN (`XXX XXX XXX`), phone (`+61XXXXXXXXX`)

### TypeScript Patterns
```typescript
// ✅ Correct - Use database types explicitly
import type { TableRow, TableInsert } from '@/contexts/OnboardingContext';
const createContact = async (data: TableInsert<'contacts'>) => {
  return await createRecord('contacts', data);
};

// ✅ Correct - Use consolidated hooks
import { useEntityListManagement } from '@/hooks/use-entity-list-management';

// ❌ Avoid - Direct Supabase calls
const { data } = await supabase.from('contacts').insert(data);
```

## High-Level Architecture

### Technology Stack
- **Frontend**: React 18 + TypeScript + Vite
- **UI Components**: shadcn/ui with Radix UI primitives + Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Edge Functions + Storage)
- **State Management**: React Context (OnboardingContext) + React Query (@tanstack/react-query)
- **Forms**: React Hook Form + Zod validation
- **Routing**: React Router DOM
- **Testing**: Vitest + Testing Library + jsdom
- **Build Tool**: Vite with SWC for fast compilation
- **Package Manager**: npm (with package-lock.json)

### Core Business Domain
NewTerra is an agricultural business administration service that provides professional bookkeeping, compliance, and administrative services designed specifically for Australian agriculture. The primary application is a **multi-step onboarding wizard** that collects comprehensive business, operational, and financial data from new farming clients to enable outsourced farm business administration services.

### Database Architecture
The database follows a **hierarchical, session-centric** design:

1. **Root**: `onboarding_sessions` table (linked to authenticated user)
2. **Four Parent Step Tables**: 
   - `step_1_business_profile` (business registration, contacts, addresses)
   - `step_2_farm_operations` (activities, licenses, suppliers, contracts)
   - `step_3_financial_systems` (bookkeeping, payroll, assets)
   - `step_4_agreements` (data migration, permissions, payments, communication preferences)
3. **Child Tables**: All data tables link to their respective step parent

**Security**: All tables use Row-Level Security (RLS) with default-deny policies. Sensitive fields are encrypted via Edge Functions before database storage.

### Key Frontend Patterns

#### Context Management
- **OnboardingContext** (`src/contexts/OnboardingContext.tsx`): Central state management with generic CRUD functions
- Provides typed database operations: `createRecord`, `updateRecord`, `deleteRecord`, `upsertRecord`
- Step initialization functions: `ensureStep1BusinessProfileRecordExists`, etc.
- Helper functions available via `context.helpers.*`

#### Hook System (Consolidated)
The project uses a **consolidated hook architecture** with exports managed through `src/hooks/index.ts`:

- **Primary Hooks**:
  - `use-enhanced-step-init.ts`: Step initialization and parent record management
  - `use-entity-list-management.ts`: CRUD operations for entity lists (contacts, addresses, etc.)
  - `use-form-management.ts`: Form state management with auto-save and validation
  - `use-auto-save-form-field.ts`: Individual field auto-save functionality

- **Specialized Hooks**:
  - `use-document-management.ts`: File upload and document handling
  - `use-form-update-wrapper.ts`: Wrapper for form update operations
  - `use-selective-refresh.ts`: Optimized data refresh strategies

#### Component Architecture
- **Step Components** (`src/components/onboarding/steps/`): Main wizard steps
- **Form Components** (`src/components/form_components/`): Reusable form components with auto-save
- **UI Components** (`src/components/ui/`): shadcn/ui component library
- Each form component handles its own validation and auto-save via consolidated hooks

### Database Type System
- **Generated Types**: `src/types/database.types.ts` (auto-generated from Supabase)
- **Business Types**: `src/types/onboarding.ts` (mapped from database types)
- **Form Types**: `src/types/form-components.ts` (form-specific data structures)

### Validation & Formatting
- **Database-Level**: CHECK constraints for Australian business formats (ABN: `XX XXX XXX XXX`, phone: `+61XXXXXXXXX`)
- **Frontend Validation**: `src/utils/form-validation.ts` + Zod schemas
- **Australian-Specific**: ABN/ACN validation, phone number formatting
- **Real-time**: Field validation on blur/change with debounced auto-save

### Edge Functions (Supabase)
Located in `supabase/functions/`:
- `load-or-create-onboarding-session/`: Session management and initialization
- `encrypt-and-store-sensitive-field/`: Secure credential storage with encryption
- `initiate-secure-file-upload/` + `finalize-secure-file-upload/`: Multi-step secure file uploads
- `import-asset-registry-from-csv/`: Bulk asset imports from CSV files
- `process-digital-signature-and-consent/`: Digital signature capture and processing
- `finalize-onboarding-submission/`: Complete onboarding submission workflow
- `mirror-to-sharepoint/`: External SharePoint integration for document mirroring
- `validate-australian-business-number/`: ABN validation via external API
- `delete-secure-document/`: Secure document deletion
- `cleanup-orphan-storage-files/`: Storage maintenance and cleanup
- `sharepoint-webhook-reciever/`: Webhook handler for SharePoint events

## Consolidated Hook System - CRITICAL FOR CLAUDE CODE

### Core Hooks (src/hooks/) - USE THESE EXCLUSIVELY

#### Primary Development Hooks

**`use-enhanced-step-init.ts`** - Step initialization and parent record management
- **Purpose**: Ensures step parent records exist before accessing child data
- **Key Function**: Prevents race conditions and database constraint violations
- **When to Use**: In all step components to initialize step parent records
```typescript
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';

const { isLoading, effectiveStepId, initializationError, handleRetryInitialization } = useEnhancedStepInit({
  stepName: 'BusinessProfileStep',
  sessionId,
  contextLoading,
  existingStepId: getStepId(sessionData, 'step1'),
  ensureStepFunction: ensureStep1BusinessProfileRecordExists,
});
```

**`use-entity-list-management.ts`** - CRUD operations for entity lists
- **Purpose**: Comprehensive entity list management with validation and constraints
- **Entities**: contacts, addresses, key_staff, activities, licenses, suppliers, etc.
- **Features**: Add/update/delete, constraint checking, error handling, UUID validation
```typescript
import { useEntityListManagement } from '@/hooks/use-entity-list-management';

const contactManagement = useEntityListManagement<ContactFormData, TableInsert<'contacts'>, TableUpdate<'contacts'>>({
  stepId: effectiveStepId,
  tableName: 'contacts',
  entities: sessionData?.step1_businessProfile?.contacts || [],
  createDefaultEntity: (stepId: string) => ({
    step_1_id: stepId,
    name: '',
    contact_type: 'Main Contact' as ContactTypeEnum
  }),
  entityDisplayName: 'Contact',
  maxEntities: 10,
  minEntities: 1
});
```

**`use-form-management.ts`** - Form state management with auto-save
- **Purpose**: Multi-field form management with debounced auto-save
- **When to Use**: For forms with multiple related fields (business registration, etc.)
```typescript
import { useFormManagement, useFieldManagement } from '@/hooks/use-form-management';

// Multi-field form management
const { formData, updateField, isValid, errors, isDirty } = useFormManagement({
  stepId: effectiveStepId,
  tableName: 'business_registration',
  initialData: sessionData?.step1_businessProfile?.businessRegistration,
  validateField: (field, value) => validateBusinessRegistrationField(field, value)
});

// Individual field auto-save
const { value, onChange, onBlur, error, isSaving } = useFieldManagement({
  entityId: entity.id,
  tableName: 'business_registration',
  fieldName: 'business_name',
  initialValue: entity.business_name,
  updateFn: updateRecord,
  validationRules: [{ rule: 'required', message: 'Business name is required' }]
});
```

#### Specialized Hooks

**`use-document-management.ts`** - File upload and document handling
- **Purpose**: Secure file uploads with Supabase Storage + SharePoint mirroring
- **Process**: initiate → upload → finalize → mirror to SharePoint

**`use-selective-refresh.ts`** - Optimized data refresh strategies
- **Purpose**: Targeted session data refresh for specific tables
- **Performance**: Reduces unnecessary re-renders and API calls

**`use-form-update-wrapper.ts`** - Wrapper for form update operations
- **Purpose**: Standardized update handling with error management

#### Deprecated Hooks (DO NOT USE)
- `useAutoSaveFormField` → Use `useFieldManagement` from `useFormManagement`
- `useDynamicEntityList` → Use `useEntityListManagement`

**Note:** These deprecated hooks are still exported from `src/hooks/index.ts` for backward compatibility but should not be used in new code.

## Component Architecture & File Organization

### Step Components (`src/components/onboarding/steps/`)
Main wizard step components that orchestrate the onboarding flow:

**`BusinessProfileStep.tsx`** - Step 1: Business registration, contacts, addresses, key staff
- **Dependencies**: `useEnhancedStepInit`, `useEntityListManagement`
- **Child Components**: BusinessRegistrationForm, ContactForm, AddressForm, KeyStaffForm
- **Data Tables**: `step_1_business_profile`, `business_registration`, `contacts`, `addresses`, `key_staff`

**`FarmOperationsStep.tsx`** - Step 2: Activities, licenses, suppliers, contracts, chemical usage
- **Dependencies**: Activities, licenses, suppliers, contracts, chemical usage management
- **Data Tables**: `step_2_farm_operations`, `activities`, `licenses`, `suppliers`, `contracts`, `chemical_usage`

**`FinancialSystemsStep.tsx`** - Step 3: Bookkeeping, payroll, asset management
- **Dependencies**: Financial system integration, asset CSV import
- **Data Tables**: `step_3_financial_systems`, `bookkeeping`, `payroll`, `assets`

**`AgreementsStep.tsx`** - Step 4: Data migration, permissions, payments, communication preferences
- **Dependencies**: Digital signature processing, payment encryption
- **Data Tables**: `step_4_agreements`, `data_migration`, `permissions`, `payments`, `communication_preferences`, `agreements`

### Form Components (`src/components/form_components/`)
Reusable form components with auto-save functionality:

**Core Form Infrastructure:**
- **`FormSection.tsx`** - Card-based section wrapper with loading/error states
- **`FormField.tsx`** - Individual field component with validation and auto-save
- **`EntityFormList.tsx`** - Generic list management for entities

**Entity-Specific Forms:**
- **`BusinessRegistrationForm.tsx`** - Business details, ABN/ACN validation
- **`BusinessInfoForm.tsx`** - Additional business information form
- **`ContactForm.tsx`** - Contact information with Australian phone formatting
- **`AddressForm.tsx`** - Australian address formatting
- **`KeyStaffForm.tsx`** - Staff member details
- **`ActivityForm.tsx`** - Farm activity types and descriptions
- **`LicenseForm.tsx`** - License management with expiry tracking
- **`SupplierForm.tsx`** - Supplier information and services
- **`ContractForm.tsx`** - Contract details linked to suppliers
- **`ChemicalUsageForm.tsx`** - Chemical application tracking
- **`AssetForm.tsx`** - Asset registry (vehicles, equipment, insurance)
- **`AssetCsvImport.tsx`** - CSV import functionality for bulk asset uploads
- **`BookkeepingForm.tsx`** - Bookkeeping software and credentials (encrypted)
- **`PayrollForm.tsx`** - Payroll system details (encrypted)
- **`DocumentIndicator.tsx`** - Document upload status indicator

### UI Components (`src/components/ui/`)
shadcn/ui component library - standard Radix UI primitives with Tailwind styling

### Utility Files - CRITICAL HELPERS

#### Validation & Formatting (`src/utils/`)

**`form-validation.ts`** - Centralized field validation
- **Functions**: `validateFormField()`, `validateForm()`
- **Rules**: required, email, phone, abn, acn, numeric, positive, minLength, maxLength
- **Usage**: Used by all form fields for consistent validation
```typescript
import { validateFormField, ValidationConfig } from '@/utils/form-validation';

const validationRules: ValidationConfig[] = [
  { rule: 'required', message: 'Business name is required' },
  { rule: 'maxLength', value: 100, message: 'Business name too long' }
];
const error = validateFormField(value, validationRules);
```

**`onboarding-validation.ts`** - Australian business-specific validation
- **Functions**: `validateABN()`, `validateACN()`, `validateAustralianPhone()`, `validateEmail()`
- **Step Validation**: `validateStep1()`, `validateCompleteOnboarding()`
- **Usage**: Step-level validation and Australian business format checking

**`form-formatters.ts`** - Real-time input formatting
- **Functions**: `formatPhoneNumber()`, `formatAbn()`, `formatAcn()`, `formatCurrency()`
- **Normalization**: `normalizePhoneNumber()`, `normalizeBusinessNumber()`
- **Usage**: Real-time formatting as user types

**`business-constants.ts`** - Business domain constants
- **Constants**: BUSINESS_STRUCTURES, CONTACT_TYPES, ACTIVITY_TYPES, LICENSE_TYPES, ASSET_CATEGORIES
- **Usage**: Dropdown options and validation enums

**`onboarding-context-helpers.ts`** - Context utility functions
- **Functions**: `getStepData()`, `getStepId()`, `isSessionDataValid()`, `formatPostgresError()`
- **Usage**: Helper functions for working with session data

#### Form Management Helpers (`src/utils/`)

**`form-management-helpers.ts`** - Shared form utilities
- **Constants**: STEP_COLUMN_MAPPING - maps table names to step foreign keys
- **Functions**: `createValidationRules()`, `sanitizeFormData()`, `generateEntityDisplayName()`
- **Usage**: Supporting utilities for form management hooks

**`validation-patterns.ts`** - Regex patterns and validation constants
- **Patterns**: Australian business number formats, phone number patterns
- **Usage**: Consistent validation across the application

### API Integration (`src/integrations/supabase/`)

**`client.ts`** - Supabase client configuration
- **Setup**: Authenticated Supabase client with proper URL and keys

**`api.ts`** - Edge Function wrappers
- **Session Management**: `loadOrCreateOnboardingSession()`, `getOnboardingSession()`
- **Validation Services**: `validateAbn()` - Australian Business Register lookup
- **File Upload**: `initiateSecureUpload()`, `finalizeSecureUpload()` - Multi-step secure uploads
- **Asset Management**: `importAssetsCSVApi()` - Bulk asset import from CSV
- **Digital Signatures**: `processDigitalSignature()` - Digital signature processing
- **Encryption**: `invokeEncryptAndStoreSensitiveField()` - Secure credential storage
- **Submission**: `submitOnboardingSession()` - Final onboarding submission
- **External Integration**: `mirrorToSharePoint()` - Document mirroring

### Type System (`src/types/`)

**`database.types.ts`** - Auto-generated Supabase types
- **Source**: Generated from Supabase schema
- **Usage**: ALL database operations must reference these types
- **Key Types**: `Database['farms']['Tables']`, row/insert/update types per table

**`onboarding.ts`** - Business logic types
- **Mapped Types**: Business-friendly interfaces mapped from database types
- **Enums**: Exported enums for form validation and business logic
- **Usage**: Component props and business logic interfaces

**`form-components.ts`** - Form-specific type definitions
- **Form Data Types**: Typed interfaces for each form component
- **Validation Types**: Interfaces for validation rules and configurations
- **Props Types**: Standard prop interfaces for reusable components

## Database Integration & CRUD Patterns

### OnboardingContext (`src/contexts/OnboardingContext.tsx`) - THE CORE

**Generic CRUD Functions (USE EXCLUSIVELY):**
```typescript
// ✅ Always use these instead of direct Supabase calls
const { createRecord, updateRecord, deleteRecord, upsertRecord } = useOnboarding();

// Create with explicit typing
const newContact: TableInsert<'contacts'> = {
  step_1_id: stepId,
  name: 'John Doe',
  contact_type: 'Main Contact'
};
const result = await createRecord('contacts', newContact);

// Update with partial data
const updates: TableUpdate<'contacts'> = { name: 'Jane Doe' };
const updated = await updateRecord('contacts', contactId, updates);
```

**Step Initialization Functions:**
```typescript
// Ensure step parent records exist before accessing child data
const { ensureStep1BusinessProfileRecordExists, ensureStep2FarmOperationsRecordExists } = useOnboarding();

const step1Id = await ensureStep1BusinessProfileRecordExists(sessionId);
```

**Helper Functions:**
```typescript
const { helpers } = useOnboarding();
// Use for data extraction, validation, formatting
const stepId = helpers.getStepId(sessionData, 'step1');
const formattedPhone = helpers.formatPhoneNumber(rawPhone);
const isValidABN = helpers.validateABN(abn);
```

### Database Table Relationships

**Hierarchical Structure:**
1. `onboarding_sessions` (root, linked to auth.users)
2. Step parent tables:
   - `step_1_business_profile` → `business_registration`, `contacts`, `addresses`, `key_staff`
   - `step_2_farm_operations` → `activities`, `licenses`, `suppliers`, `contracts`, `chemical_usage`
   - `step_3_financial_systems` → `bookkeeping`, `payroll`, `assets`
   - `step_4_agreements` → `data_migration`, `permissions`, `payments`, `communication_preferences`, `agreements`

**Step Column Mapping (from form-management-helpers.ts):**
```typescript
const STEP_COLUMN_MAPPING = {
  // Step 1 tables
  'addresses': 'step_1_id',
  'contacts': 'step_1_id',
  'business_registration': 'step_1_id',
  'key_staff': 'step_1_id',
  // Step 2 tables
  'activities': 'step_2_id',
  'licenses': 'step_2_id',
  // ... etc
};
```

## Critical Development Guidelines

### 1. Always Use Consolidated Hooks
```typescript
// ✅ Correct approach
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';

// ❌ Never use deprecated hooks
import { useAutoSaveFormField } from '@/hooks/use-auto-save-form-field'; // DEPRECATED
```

### 2. Explicit Type Casting with Database Types
```typescript
// ✅ Always reference database.types.ts explicitly
import type { TableRow, TableInsert, TableUpdate } from '@/contexts/OnboardingContext';
import type { Database } from '@/types/database.types';

// Explicit type casting for all database operations
const contactData: TableInsert<'contacts'> = {
  step_1_id: stepId,
  name: formData.name,
  contact_type: formData.contact_type as Database['farms']['Enums']['contact_type_enum']
};
```

### 3. Step Initialization Pattern
```typescript
// ✅ Always follow this pattern in step components
const { isLoading, effectiveStepId, initializationError } = useEnhancedStepInit({
  stepName: 'BusinessProfileStep',
  sessionId,
  contextLoading,
  existingStepId: getStepId(sessionData, 'step1'),
  ensureStepFunction: ensureStep1BusinessProfileRecordExists,
});

// Always check for effective step ID before proceeding
if (!effectiveStepId) {
  return <StepLoadingState />;
}
```

### 4. Australian Business Validation
```typescript
// ✅ Use validation utilities for Australian formats
import { validateABN, validateACN, validateAustralianPhone } from '@/utils/onboarding-validation';
import { formatAbn, formatPhoneNumber } from '@/utils/form-formatters';

// Validate before saving
if (!validateABN(abn)) {
  setError('Invalid ABN format');
  return;
}

// Format in real-time
const formattedPhone = formatPhoneNumber(userInput);
```

### 5. Error Handling & User Feedback
```typescript
// ✅ Comprehensive error handling
try {
  const result = await createRecord('contacts', contactData);
  toast({ title: "Success", description: "Contact added successfully" });
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  logger.error('Failed to create contact:', error);
  toast({
    title: "Error",
    description: `Failed to add contact: ${errorMessage}`,
    variant: "destructive"
  });
}
```

### 6. Security & Encryption
```typescript
// ✅ Sensitive data MUST be encrypted via Edge Functions
const { encryptAndStoreSensitiveField } = useOnboarding();

const result = await encryptAndStoreSensitiveField({
  tableName: 'bookkeeping',
  fieldName: 'access_credentials',
  plainTextValue: credentials
});

// ❌ Never store sensitive data directly
const badResult = await updateRecord('bookkeeping', id, {
  access_credentials: credentials // This will fail - requires encryption
});
```

## File Reference Quick Guide

### When to Edit Which Files:

**Adding a new form field:**
- Edit specific form component (e.g., `ContactForm.tsx`)
- Update type in `form-components.ts` if needed
- Add validation rules in `form-validation.ts`

**Adding a new entity type:**
- Create form component in `form_components/`
- Add to relevant step component
- Update `useEntityListManagement` configuration
- Add database table mapping in `form-management-helpers.ts`

**Adding validation:**
- Business-specific: `onboarding-validation.ts`
- General rules: `form-validation.ts`
- Constants: `business-constants.ts`

**Adding API integration:**
- Edge Function wrapper: `api.ts`
- Type definitions for results in `api.ts`

**Fixing bugs:**
- Hook issues: Check `src/hooks/CONSOLIDATION_GUIDE.md`
- Validation: `src/utils/form-validation.ts` or `onboarding-validation.ts`
- Database operations: `OnboardingContext.tsx`
- UI components: Specific component files

### Testing Strategy
- **Unit tests**: Located in `test/` directory
- **Focus areas**: Business validation (ABN, phone), form auto-save, entity CRUD
- **Tools**: Vitest + Testing Library + jsdom (configured in `vitest.config.ts`)
- **Patterns**: Test consolidated hooks behavior, validation edge cases
- **Status**: Basic test infrastructure with initial test files for business profile and utilities

### Security Best Practices
- **Never store sensitive data unencrypted** - use Edge Function encryption
- **Validate all user inputs** - client and server-side validation
- **Follow RLS policies** - respect hierarchical data access
- **Use proper TypeScript types** - prevent runtime type errors
- **Sanitize database queries** - use parameterized queries via Supabase

### Performance Considerations
- Use `useSelectiveRefresh` for targeted data updates
- Implement proper debouncing in form fields (1000ms default)
- Leverage React.memo for entity list items
- Avoid unnecessary re-renders through proper dependency arrays
- Monitor bundle size with build analysis
- Optimize image loading and caching strategies

## Application Routing & Navigation (`src/App.tsx`, `src/pages/`)

### Routing Structure
- **`/`** - Landing page (`Index.tsx`) - public marketing page
- **`/auth`** - Authentication page (`Auth.tsx`) - login/register/forgot password
- **`/onboarding`** - Protected onboarding wizard (`Onboarding.tsx`) - requires authentication
- **`/*`** - 404 catch-all (`NotFound.tsx`)

### Context Providers Hierarchy
```typescript
// Critical provider order - DO NOT CHANGE
<QueryClientProvider> // React Query for server state
  <AuthProvider> // User authentication state
    <ProtectedRoute> // Route protection wrapper
      <ErrorBoundary> // Error catching for onboarding
        <OnboardingProvider> // Onboarding session management
          <Onboarding /> // Main wizard component
```

### Authentication System (`src/contexts/AuthContext.tsx`)

**Key Features:**
- Supabase Auth integration with email/password
- Session persistence and state management
- Route protection with redirect functionality
- Password reset via email

**Auth Flow:**
1. `AuthProvider` manages global auth state
2. `ProtectedRoute` wraps protected pages
3. Unauthenticated users redirect to `/auth?redirectTo=/onboarding`
4. Post-login redirect to intended destination

**Security Considerations:**
- User metadata includes: full_name, username, phone_number, receive_text_messages
- Email confirmation required for signup
- Password reset uses secure email flow

### Onboarding Wizard (`src/pages/Onboarding.tsx`)

**Step Configuration:**
```typescript
const STEPS = [
  { id: 1, title: "Business Profile", component: BusinessProfileStep },
  { id: 2, title: "Farm Operations", component: FarmOperationsStep },
  { id: 3, title: "Financial Systems", component: FinancialSystemsStep },
  { id: 4, title: "Agreements & Review", component: AgreementsStep }
];
```

**Navigation Logic:**
- `currentStep` stored in database (`onboarding_sessions.current_step`)
- `updateCurrentStep()` persists progress
- Step validation controls navigation (currently bypassed with `canGoNext={true}`)

## Edge Functions & Database Architecture

### Edge Functions (`supabase/functions/`)

**Session Management:**
- **`load-or-create-onboarding-session/`** - Creates or retrieves user's onboarding session
  - Ensures single session per user
  - Calls `get_onboarding_session_data` RPC function
  - Returns full hierarchical session data

**Security Functions:**
- **`encrypt-and-store-sensitive-field/`** - Encrypts sensitive data before storage
  - Uses CryptoJS for AES encryption
  - Allowed tables: bookkeeping, payments, payroll
  - Allowed fields: access_credentials, bank_account_details, encrypted_access_credentials
  - Calls `public.update_encrypted_field` database function

**File Management:**
- **`initiate-secure-file-upload/`** - Generates signed upload URLs
- **`finalize-secure-file-upload/`** - Creates document records post-upload
- **`mirror-to-sharepoint/`** - External system integration

**Business Logic:**
- **`validate-australian-business-number/`** - ABN verification via external API
- **`import-asset-registry-from-csv/`** - Bulk asset import
- **`process-digital-signature-and-consent/`** - Digital signature storage
- **`finalize-onboarding-submission/`** - Complete submission workflow

### Database Migrations (`supabase/migrations/`)

**Migration Files:**
1. **`0000_initial_schema.sql`** - Complete farms schema with hierarchical structure
2. **`0001_rls_policies.sql`** - Row-Level Security policies (default-deny)
3. **`0002_edge_functions.sql`** - Database functions for Edge Functions
4. **`0003_storage_setup.sql`** - Supabase Storage bucket and policies
5. **`0004_onbaording_sync.sql`** - Additional onboarding synchronization features

**RLS Security Model:**
- All tables use `auth.uid()` for user isolation
- Hierarchical policies cascade from `onboarding_sessions.user_id`
- Service role bypass for Edge Functions
- Default deny with explicit grants

### Critical Database Functions

**`get_onboarding_session_data()`** - Core RPC function
- Returns complete nested session data
- Single source of truth for frontend state
- Optimized joins across all related tables

**`public.update_encrypted_field()`** - Encryption helper
- Called by encrypt-and-store-sensitive-field Edge Function
- Handles BYTEA field updates securely
- Validates table and field permissions

## Error Handling & Logging Patterns

### Logger Utility (`src/utils/logger.ts`)
- Centralized logging with structured format
- Context-aware error tracking
- Performance monitoring capabilities

### Error Boundary Strategy
- `ErrorBoundary` wraps onboarding wizard
- Graceful fallback UI for component crashes
- Error reporting with user-friendly messages

### Toast Notification System
- Global toast provider via Sonner
- Consistent success/error messaging
- Auto-dismiss with appropriate durations
- Accessibility compliance

## Performance Optimization Patterns

### React Query Integration
- Server state management for API calls
- Automatic caching and invalidation
- Background refetching strategies
- Optimistic updates for better UX

### Component Optimization
- `React.memo` for entity list items
- Proper dependency arrays in hooks
- Debounced form updates (1000ms default)
- Selective refresh patterns

### Database Optimization
- Strategic indexes on foreign keys
- Optimized RPC functions with minimal queries
- Hierarchical data loading
- Connection pooling via Supabase

## Testing Strategy & Quality Assurance

### Test Structure (`test/`)
- **Framework**: Vitest + Testing Library + jsdom (configured)
- **Configuration**: `vitest.config.ts` with React SWC and path aliases
- **Existing Tests**:
  - `businessProfileStep.test.tsx` - Business profile step component testing
  - `cn.test.ts` - Utility function testing
- **Status**: Basic test infrastructure with initial test files

### Code Quality Tools
- ESLint with React/TypeScript rules
- TypeScript strict mode enabled
- Consistent formatting and naming conventions
- Import/export organization

## Deployment & Environment Configuration

### Build Configurations
- **Development**: `npm run build:dev` - with dev optimizations
- **Production**: `npm run build` - optimized bundle
- **Preview**: `npm run preview` - test production build

### Environment Variables
Required environment variables in `.env`:
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Supabase anonymous key
- Edge Function environment setup
- CORS and security headers

### Supabase Configuration (`supabase/config.toml`)
- Database connection settings
- Edge Function runtime configuration
- Storage bucket policies
- Auth provider settings

## Current Development Status & Roadmap

### ✅ Completed Features
- **Complete Database Schema**: Hierarchical design with 4-step structure
- **All Onboarding Steps**: Business Profile, Farm Operations, Financial Systems, Agreements
- **Consolidated Hook Architecture**: useEnhancedStepInit, useEntityListManagement, useFormManagement
- **Australian Business Validation**: ABN, ACN, phone number formatting and validation
- **Secure File Management**: Multi-step upload with SharePoint mirroring
- **Digital Signatures**: Secure signature capture and storage
- **Encrypted Credentials**: Secure storage of sensitive business data
- **Edge Functions**: 12 functions for secure operations

### 🚧 In Progress
- **Testing Infrastructure**: Basic tests exist, comprehensive suite needed
- **Documentation**: Ongoing updates and improvements
- **Performance Optimizations**: Auto-save timing and entity list rendering

### ⚠️ Known Issues & Technical Debt

**Immediate Attention Required:**
1. **Security**: CORS headers too permissive in Edge Functions
2. **UX**: Missing step validation in onboarding navigation
3. **Logging**: Production console.log statements in AuthContext
4. **Race Conditions**: Basic session handling in AuthContext
5. **TODO Items**: Incomplete submission workflow
6. **Testing**: Limited test coverage - only 2 test files exist

**Performance Considerations:**
1. Large form state management could benefit from optimization
2. Entity list rendering with many items needs virtualization
3. File upload progress indicators missing
4. Debounce timing may need adjustment based on user testing

### 🔮 Future Enhancements
1. **Comprehensive Testing**: Full test suite for all components and hooks
2. **Real-time Collaboration**: Multi-user editing capabilities
3. **Offline Support**: Form data persistence without internet
4. **Advanced Validation**: Rules engine for complex business logic
5. **Audit Trail**: Complete data change tracking
6. **External Integrations**: Additional accounting software support
7. **Mobile Application**: Native mobile app development

---

This comprehensive documentation enables developers to navigate the solid-camera codebase efficiently, understand the consolidated architecture, and implement changes following established patterns while maintaining type safety and business logic consistency.